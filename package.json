{"name": "rsglider-api", "version": "1.0.0", "description": "RSGlider API - Comprehensive platform with developer marketplace, BTCPay integration, and session management", "author": "RSGlider Team", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build && tsc-alias", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/src/main.js", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest --passWithNoTests --silent", "test:watch": "jest --watch --silent", "test:cov": "jest --coverage --silent", "test:cov:detailed": "jest --coverage --coverageReporters=html --coverageReporters=text --coverageReporters=json --coverageReporters=lcov --silent", "test:cov:tree": "jest --coverage --coverageReporters=html --coverageReporters=text-summary --silent && echo '\n📊 Coverage tree report generated in coverage/lcov-report/index.html'", "test:cov:threshold": "jest --coverage --coverageThreshold='{\"global\":{\"branches\":70,\"functions\":70,\"lines\":70,\"statements\":70}}' --silent", "test:cov:watch": "jest --coverage --watchAll --silent", "test:cov:nyc": "nyc --reporter=html --reporter=text --reporter=json jest --silent", "test:cov:c8": "c8 --reporter=html --reporter=text --reporter=json jest --silent", "test:cov:open": "pnpm run test:cov:detailed && open coverage/lcov-report/index.html", "test:cov:report": "jest --coverage --passWithNoTests --coverageReporters=html --coverageReporters=text --coverageReporters=json --coverageReporters=lcov --coverageReporters=clover --silent", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:all": "pnpm run test", "test:integration": "tsx test-s3-integration.ts", "test:gitea": "tsx test-gitea-integration.ts", "test:security": "tsx test-security-permissions.ts", "test:standalone": "pnpm run test:integration && pnpm run test:gitea && pnpm run test:security", "test:docker": "docker-compose exec api pnpm run test", "test:auth:docker": "docker-compose exec api pnpm run test:auth", "test:gitea:docker": "docker-compose exec api pnpm run test:gitea", "validate:api": "spectral lint api-docs/openapi.yaml", "lint:api": "spectral lint api-docs/openapi.yaml --format=stylish", "docker:setup": "./scripts/dev-setup.sh", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:logs:api": "docker-compose logs -f api", "docker:restart:api": "docker-compose restart api", "db:shell": "docker-compose exec postgres psql -U rsglider -d rsglider", "redis:shell": "docker-compose exec redis redis-cli -a rsglider_redis_password", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio"}, "dependencies": {"@aws-sdk/client-s3": "^3.820.0", "@aws-sdk/s3-request-presigner": "^3.820.0", "@nestjs/common": "^11.1.2", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.2", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.2", "@nestjs/swagger": "^11.2.0", "@nestjs/throttler": "^6.4.0", "axios": "^1.9.0", "bcrypt": "^6.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "connect-redis": "^8.1.0", "dotenv": "^16.5.0", "drizzle-kit": "^0.31.1", "drizzle-orm": "^0.44.0", "drizzle-zod": "^0.8.2", "express-session": "^1.18.1", "form-data": "^4.0.2", "mime-types": "^3.0.1", "multer": "^2.0.0", "nestjs-drizzle": "^1.1.6", "openapi-types": "^12.1.3", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.16.0", "postgres": "^3.4.7", "qrcode": "^1.5.4", "redis": "^5.1.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "speakeasy": "^2.0.0", "swagger-ui-express": "^5.0.1", "uuid": "^11.1.0", "zod": "^3.25.42"}, "devDependencies": {"@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.1.2", "@stoplight/spectral-cli": "^6.15.0", "@types/bcrypt": "^5.0.2", "@types/express": "^5.0.2", "@types/express-session": "^1.18.1", "@types/jest": "^29.5.14", "@types/js-yaml": "^4.0.9", "@types/mime-types": "^3.0.0", "@types/multer": "^1.4.12", "@types/node": "^22.15.27", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/qrcode": "^1.5.5", "@types/speakeasy": "^2.0.10", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.33.0", "@typescript-eslint/parser": "^8.33.0", "c8": "^10.1.3", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "istanbul-reports": "^3.1.7", "jest": "^29.7.0", "js-yaml": "^4.1.0", "nodemon": "^3.1.10", "nyc": "^17.1.0", "prettier": "^3.5.3", "source-map-support": "^0.5.21", "supertest": "^7.1.1", "ts-jest": "^29.3.4", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsc-alias": "^1.8.16", "tsconfig-paths": "^4.2.0", "tsx": "^4.19.4", "typescript": "^5.8.3"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": ["ts-jest", {"tsconfig": {"module": "commonjs"}}]}, "collectCoverageFrom": ["**/*.(t|j)s", "!**/*.spec.ts", "!**/node_modules/**", "!**/dist/**", "!**/coverage/**", "!**/*.d.ts", "!**/main.ts", "!**/test/**"], "coverageDirectory": "../coverage", "coverageReporters": ["text", "text-summary", "html", "json", "lcov", "clover"], "coverageThreshold": {"global": {"branches": 60, "functions": 60, "lines": 60, "statements": 60}}, "moduleNameMapper": {"^@/(.*)$": "<rootDir>/$1", "^(\\.{1,2}/.*)\\.js$": "$1"}, "setupFilesAfterEnv": ["<rootDir>/../test/setup.ts"], "transformIgnorePatterns": ["node_modules/(?!(.*\\.mjs$))"], "maxWorkers": 1, "forceExit": true, "detectOpenHandles": true, "verbose": false, "silent": false, "reporters": ["default"]}, "engines": {"node": ">=22.0.0", "npm": ">=10.0.0"}, "keywords": ["<PERSON><PERSON><PERSON>", "api", "openapi", "swagger", "btcpay", "g<PERSON>a", "marketplace", "bitcoin", "developer-platform"], "repository": {"type": "git", "url": "git+https://github.com/rsglider/rsglider-api.git"}, "bugs": {"url": "https://github.com/rsglider/rsglider-api/issues"}, "homepage": "https://docs.rsglider.com", "main": "index.js", "directories": {"doc": "docs"}, "type": "module", "packageManager": "pnpm@10.11.0+sha512.6540583f41cc5f628eb3d9773ecee802f4f9ef9923cc45b69890fb47991d4b092964694ec3a4f738a420c918a333062c8b925d312f42e4f0c263eb603551f977"}