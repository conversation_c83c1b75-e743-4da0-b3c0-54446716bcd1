# RSGlider API

> Comprehensive NestJS API for the RSGlider platform with developer marketplace, BTCPay Server integration, and advanced session management.

## 🚀 Features

### 🔐 **Authentication & Security**
- JWT-based authentication with refresh token rotation
- Two-factor authentication (TOTP) with backup codes
- Role-based access control (RBAC)
- Comprehensive session management across platforms
- **SecureLogger**: Automatic sanitization of sensitive data in logs
- **Production-safe error handling**: Database queries and sensitive info never exposed

### 🧑‍💻 **Developer Marketplace**
- Admin-controlled Gitea integration with SSO
- Repository-to-marketplace publishing
- Real-time analytics and revenue tracking
- Flexible BTCPay Server payouts (Bitcoin-only)

### 💳 **Payment System**
- Direct Bitcoin payments via BTCPay Server
- No stored funds (security-first approach)
- Real-time payment tracking with webhooks
- Automated revenue sharing and payouts

### 📱 **Multi-Platform Session & Device Control**
- Desktop app (Tauri) device registration and limits
- Web app device verification with email validation
- Bot session management with subscription-based limits
- Cross-platform session synchronization
- **Device management API:** Register, list, and remove devices for user accounts

## 📁 Project Structure

```
rsglider-api/
├── api-docs/                      # API Documentation
│   ├── openapi.yaml              # Complete OpenAPI 3.0.3 specification
│   ├── README.md                 # API documentation overview
│   ├── nestjs-integration.md     # NestJS implementation guide
│   └── examples/                 # Testing resources
│       ├── postman-collection.json
│       ├── environments.json
│       └── curl-examples.md
├── docs/                         # Implementation guides
│   ├── BTCPAY_INTEGRATION.md
│   ├── GITEA_INTEGRATION_PLAN.md
│   └── SESSION_MANAGEMENT.md
├── src/                          # NestJS source code (to be created)
├── .spectral.yml                 # OpenAPI linting rules
├── .yamllint.yml                 # YAML formatting rules
├── .yamlvalidate                 # YAML validation config
└── package.json                  # Dependencies and scripts
```

## 🛠️ Development Setup

### Prerequisites
- Node.js 22+ LTS and npm 10+
- Docker and Docker Compose
- PostgreSQL 17 (via Docker)
- Redis 7 (via Docker)

### Quick Start
```bash
# Clone repository
git clone https://github.com/rsglider/rsglider-api.git
cd rsglider-api

# Set up local infrastructure (PostgreSQL + Redis)
ppnpm run docker:setup

# Install Node.js dependencies
npm install

# Start development server
pnpm run start:dev
```

### Manual Setup
```bash
# Start just the databases
pnpm run docker:up

# Stop databases
pnpm run docker:down

# View database logs
pnpm run docker:logs

# Access database shell
pnpm run db:shell

# Access Redis shell
pnpm run redis:shell
```

### Local Development
The setup script creates a `.env` file with development defaults:

```bash
# Database (Docker)
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=rsglider
DATABASE_USER=rsglider
DATABASE_PASSWORD=rsglider_dev_password

# Redis (Docker)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=rsglider_redis_password

# JWT (Development - change in production!)
JWT_SECRET=dev_jwt_secret_change_in_production
```

For production, copy `.env.example` and update with real values.

## 📚 API Documentation

### Interactive Documentation
- **Development**: http://localhost:3000/api/docs
- **Staging**: https://api-staging.rsglider.com/api/docs
- **Production**: https://api.rsglider.com/api/docs

### Testing Resources
- **Postman Collection**: `api-docs/examples/postman-collection.json`
- **cURL Examples**: `api-docs/examples/curl-examples.md`
- **Environment Files**: `api-docs/examples/environments.json`

## 🧪 Testing & Validation

### API Validation
```bash
# Validate OpenAPI specification
pnpm run validate:api

# Lint YAML files
pnpm run validate:yaml

# Check OpenAPI compliance
pnpm run validate:openapi

# Run Postman tests
pnpm run postman:test
```

### Unit & Integration Tests

#### 🔇 **Clean Testing (Default)**
```bash
# Run all tests with clean output
pnpm test

# Run tests in watch mode
pnpm run test:watch

# Run tests with coverage
pnpm run test:cov

# Run specific test file
pnpm test src/auth/auth.service.spec.ts
```

#### 🔊 **Debug Testing (Full Logs)**
When you need to see all NestJS service logs, database errors, and debug information:

```bash
# Run tests with full logging
TEST_DEBUG=true pnpm test

# Run coverage with full logging
TEST_DEBUG=true pnpm run test:cov

# Debug specific test file
TEST_DEBUG=true pnpm test src/auth/auth.service.spec.ts

# Debug with watch mode
TEST_DEBUG=true pnpm run test:watch
```

#### 📊 **Coverage & Reporting**
```bash
# Generate detailed coverage reports
pnpm run test:cov:detailed

# Generate tree coverage view
pnpm run test:cov:tree

# Run with coverage threshold
pnpm run test:cov:threshold

# Open coverage report in browser
pnpm run test:cov:open
```

#### 🎯 **Testing Best Practices**
- **Default mode**: Clean output for normal development
- **Debug mode**: Use `TEST_DEBUG=true` when investigating test failures
- **Integration tests**: Use real database setup with Drizzle migrations
- **Unit tests**: Focus on business logic with proper mocking
- **Coverage target**: Aim for 80% overall coverage

## 🧪 Development Tools

### Database Management
- **PostgreSQL**: localhost:5432 (use your preferred SQL client)
- **Direct access**: `pnpm run db:shell`

### Redis Management
- **Redis Commander**: http://localhost:8091 (Redis admin interface)
- **Direct access**: `pnpm run redis:shell`

### API Documentation
- **Swagger UI**: http://localhost:3000/api/docs (when NestJS is running)

## 📖 Key Endpoints

### Authentication
- `POST /auth/register` - User registration
- `POST /auth/login` - Login with 2FA support
- `POST /auth/refresh` - Token refresh

### User Management
- `GET /users/me` - Current user profile
- `GET /users/me/sessions` - Session management
- `POST /users/me/2fa/setup` - Two-factor authentication
- `GET /users/me/devices` - List registered devices
- `POST /users/me/devices` - Register a new device
- `DELETE /users/me/devices/{deviceId}` - Remove a device

### Developer Integration
- `GET /developers/gitea/profile` - Gitea integration status
- `POST /developers/repositories/{id}/publish` - Publish to marketplace
- `GET /developers/analytics/overview` - Analytics dashboard

### Marketplace
- `GET /store/items` - Browse marketplace
- `POST /cart/checkout` - BTCPay Server checkout
- `GET /orders/{id}/status` - Payment status

### Admin Controls
- `POST /admin/developers/{id}/promote` - Promote to developer
- `PUT /admin/payout-settings` - Configure payouts
- `GET /admin/users` - User management

## 🔒 Security Features

### SecureLogger Utility
The API includes a comprehensive logging system that automatically sanitizes sensitive data:

```typescript
import { SecureLogger } from '@/common/utils/secure-logger';

const logger = new SecureLogger('MyService');

// Automatically redacts sensitive fields
logger.log({ username: 'john', password: 'secret123' });
// Output: { username: 'john', password: '[REDACTED]' }

// Database errors are sanitized in production
logger.error('Failed query: SELECT * FROM users WHERE password = $1');
// Production: 'Database operation failed'
// Development: Full error details
```

#### Sensitive Field Detection
Automatically redacts fields containing:
- `password`, `token`, `secret`, `key`
- `authorization`, `auth`, `credential`
- `private`, `session`, `cookie`, `jwt`
- `refresh_token`, `access_token`, `api_key`
- `client_secret`, `client_id`

#### Specialized Logging Methods
```typescript
// Database operations
logger.logDatabaseOperation('INSERT', 'users', true);

// Authentication events
logger.logAuthEvent('login', userId, true, { ip: '127.0.0.1' });

// API requests with URL sanitization
logger.logApiRequest('GET', '/api/users?token=secret', 200, userId, 150);
```

### Global Exception Filter
- **Production**: Sanitizes all error responses and logs
- **Development**: Full error details for debugging
- **Test**: Suppressed logging to prevent noise

## 🔧 Configuration

### Payout Settings
- **Default frequency**: Weekly
- **Minimum payout**: $50 USD (Bitcoin equivalent)
- **Holding period**: 7 days
- **Revenue share**: 70% developer, 30% platform

### Session Limits (by subscription)
- **Free**: 1 desktop, 1 bot, 3 web sessions
- **Pro**: 2 desktops, 3 bots, 3 web sessions
- **Addons**: Additional desktop and bot sessions available for purchase

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and validation
5. Submit a pull request

### Code Quality
- Follow NestJS conventions
- Add Swagger decorators to all DTOs
- Include unit tests for new features
- Validate OpenAPI spec changes
- **Maintain 80% test coverage** - Use `pnpm run test:cov` to check
- **Use SecureLogger** for all logging to ensure sensitive data protection
- **Test in both modes**: Clean output for CI/CD, debug mode for investigation

## 📄 License

This project is proprietary and confidential. All rights reserved.

## 🆘 Support

- **Documentation**: https://docs.rsglider.com
- **Issues**: https://github.com/rsglider/rsglider-api/issues
- **Discord**: https://discord.gg/rsglider

---

Built with ❤️ by the RSGlider team
