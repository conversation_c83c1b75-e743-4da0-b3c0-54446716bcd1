/**
 * Jest Test Setup
 * Global test configuration and utilities
 */

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret-key-for-testing-only';
process.env.DATABASE_URL = 'sqlite://test.db';
process.env.REDIS_URL = 'redis://localhost:6379';

// Global test timeout
jest.setTimeout(30000);

// Mock console methods and NestJS Logger in tests to reduce noise
const originalConsole = { ...console };

// Create a mock Logger class that suppresses all output during tests
class MockLogger {
  log = jest.fn();
  error = jest.fn();
  warn = jest.fn();
  debug = jest.fn();
  verbose = jest.fn();
  setContext = jest.fn();
  localInstance = jest.fn();
  static log = jest.fn();
  static error = jest.fn();
  static warn = jest.fn();
  static debug = jest.fn();
  static verbose = jest.fn();
}

// Mock the NestJS Logger globally
jest.mock('@nestjs/common', () => {
  const actual = jest.requireActual('@nestjs/common');
  return {
    ...actual,
    Logger: MockLogger,
  };
});

beforeEach(() => {
  // Suppress console output during tests (except for actual test failures)
  console.log = jest.fn();
  console.warn = jest.fn();
  console.error = jest.fn(); // Also suppress error logs during tests
  console.debug = jest.fn();
  console.info = jest.fn();
});

afterEach(() => {
  // Restore console after each test
  Object.assign(console, originalConsole);
});

// Global test utilities
global.testUtils = {
  createMockUser: () => ({
    id: 'test-user-id',
    email: '<EMAIL>',
    username: 'testuser',
    isEmailVerified: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  }),

  createMockRequest: (user?: any) => ({
    user,
    headers: {},
    body: {},
    params: {},
    query: {},
  }),

  createMockResponse: () => {
    const res: any = {};
    res.status = jest.fn().mockReturnValue(res);
    res.json = jest.fn().mockReturnValue(res);
    res.send = jest.fn().mockReturnValue(res);
    res.cookie = jest.fn().mockReturnValue(res);
    res.clearCookie = jest.fn().mockReturnValue(res);
    return res;
  },
};

// Export to make this a module
export { };

// Declare global types for TypeScript
declare global {
  var testUtils: {
    createMockUser: () => any;
    createMockRequest: (user?: any) => any;
    createMockResponse: () => any;
  };
}
